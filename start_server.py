#!/usr/bin/env python3
"""
Startup script for the ICA DRG Extraction API server.
Supports both development and production modes with configurable workers.
"""

import os
import sys
import argparse
import uvicorn
from app.config import settings

def main():
    """Main function to start the server."""
    parser = argparse.ArgumentParser(description="Start the ICA DRG Extraction API server")
    parser.add_argument(
        "--workers", 
        type=int, 
        default=4, 
        help="Number of worker processes (default: 4)"
    )
    parser.add_argument(
        "--host", 
        type=str, 
        default=settings.api_host, 
        help=f"Host to bind to (default: {settings.api_host})"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=settings.api_port, 
        help=f"Port to bind to (default: {settings.api_port})"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="Enable auto-reload for development (disables multiple workers)"
    )
    parser.add_argument(
        "--log-level", 
        type=str, 
        default="info", 
        choices=["critical", "error", "warning", "info", "debug", "trace"],
        help="Log level (default: info)"
    )
    
    args = parser.parse_args()
    
    print("🚀 Starting ICA DRG Extraction API Server")
    print("=" * 50)
    print(f"Host: {args.host}")
    print(f"Port: {args.port}")
    
    if args.reload:
        print("Mode: Development (auto-reload enabled)")
        print("Workers: 1 (reload mode)")
        print("=" * 50)
        
        uvicorn.run(
            "app.main:app",
            host=args.host,
            port=args.port,
            reload=True,
            log_level=args.log_level
        )
    else:
        print("Mode: Production (multiple workers)")
        print(f"Workers: {args.workers}")
        print("=" * 50)
        
        uvicorn.run(
            "app.main:app",
            host=args.host,
            port=args.port,
            workers=args.workers,
            log_level=args.log_level
        )

if __name__ == "__main__":
    main()
