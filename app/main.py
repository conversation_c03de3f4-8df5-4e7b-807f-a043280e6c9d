"""
FastAPI application for the PDF Information Extractor.
"""
import os
import tempfile
import shutil
from typing import List
from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn

from app.config import settings
from app.utils import format_json_output
from app.document_processor import (
    extract_text_from_brd,
    extract_text_from_pdf,
    chunk_pdf_text,
    extract_text_from_brd_async,
    extract_text_from_pdf_async
)
from app.extraction_logic import (
    identify_requirements,
    process_chunks_in_parallel,
    aggregate_results,
    identify_requirements_async,
    process_chunks_in_parallel_async
)

from dotenv import load_dotenv
# Load environment variables from .env file in the project root
load_dotenv("/Users/<USER>/Documents/projects/ica-drg-extraction/.env")


# Create FastAPI app
app = FastAPI(
    title="PDF Information Extractor",
    description="Extract specific information from PDFs based on BRD requirements",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development; restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory if it doesn't exist
UPLOADS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "uploads")
PDF_UPLOADS_DIR = os.path.join(UPLOADS_DIR, "pdfs")
os.makedirs(PDF_UPLOADS_DIR, exist_ok=True)

# Mount static files directory for serving PDFs
app.mount("/pdfs", StaticFiles(directory=PDF_UPLOADS_DIR), name="pdfs")

# Store processing results and uploaded file information
processing_results = {}
uploaded_files = {}


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "PDF Information Extractor API"}


@app.post("/upload-and-process/")
async def upload_and_process(
    background_tasks: BackgroundTasks,
    brd_file: UploadFile = File(...),
    pdf_files: List[UploadFile] = File(...)
):
    """
    Upload BRD and PDF files and start the extraction process.

    Args:
        background_tasks: FastAPI background tasks
        brd_file: The BRD file (DOC or DOCX)
        pdf_files: List of PDF files

    Returns:
        Dict: Process ID for tracking the extraction process
    """
    # Log the new request
    print("\n" + "="*80)
    print(f"[REQUEST] New extraction request received")
    print(f"[INFO] BRD file: {brd_file.filename}")
    print(f"[INFO] PDF files: {', '.join([pdf.filename for pdf in pdf_files])}")
    print("="*80)
    # Validate file extensions
    brd_ext = os.path.splitext(brd_file.filename)[1].lower()
    if brd_ext not in settings.allowed_brd_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"BRD file must be one of: {', '.join(settings.allowed_brd_extensions)}"
        )

    for pdf_file in pdf_files:
        pdf_ext = os.path.splitext(pdf_file.filename)[1].lower()
        if pdf_ext not in settings.allowed_pdf_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"Document files must have one of these extensions: {', '.join(settings.allowed_pdf_extensions)}"
            )

    # Create a process ID
    process_id = f"process_{len(processing_results) + 1}"

    # Initialize result entry
    processing_results[process_id] = {
        "status": "processing",
        "message": "Files uploaded, starting extraction process",
        "result": None
    }

    # Create temporary directory for files
    temp_dir = tempfile.mkdtemp()

    try:
        # Save uploaded files to disk immediately
        brd_path = os.path.join(temp_dir, brd_file.filename)
        with open(brd_path, "wb") as f:
            content = await brd_file.read()
            f.write(content)

        pdf_paths = []
        for pdf_file in pdf_files:
            pdf_path = os.path.join(temp_dir, pdf_file.filename)
            with open(pdf_path, "wb") as f:
                content = await pdf_file.read()
                f.write(content)
            pdf_paths.append(pdf_path)

        # Start background processing with file paths instead of file objects
        # Use async version for better performance
        background_tasks.add_task(
            process_files_from_disk_async,
            process_id,
            brd_path,
            pdf_paths
        )

        return {"process_id": process_id, "status": "processing"}

    except Exception as e:
        processing_results[process_id].update({
            "status": "failed",
            "message": f"File upload failed: {str(e)}",
            "error": str(e)
        })
        return {"process_id": process_id, "status": "failed"}


@app.get("/status/{process_id}")
async def get_status(process_id: str):
    """
    Get the status of an extraction process.

    Args:
        process_id: The process ID

    Returns:
        Dict: Process status and results if available
    """
    # Log status request (but not too frequently to avoid cluttering the logs)
    import time
    current_time = time.time()
    last_log_time = getattr(get_status, 'last_log_time', 0)

    # Only log status requests every 5 seconds
    if current_time - last_log_time > 5:
        print(f"[STATUS] Status request for process {process_id}")
        get_status.last_log_time = current_time

    if process_id not in processing_results:
        print(f"[ERROR] Process ID {process_id} not found")
        raise HTTPException(
            status_code=404,
            detail=f"Process ID {process_id} not found"
        )

    # Get current status
    status = processing_results[process_id].get("status", "unknown")
    message = processing_results[process_id].get("message", "No status message")

    # Only log status changes or completed/failed statuses
    if (hasattr(get_status, 'last_status') and get_status.last_status != status) or status in ["completed", "failed"]:
        print(f"[STATUS] Process {process_id}: {status} - {message}")
        get_status.last_status = status

    # Include PDF file information if available
    if process_id in uploaded_files and "pdf_files" not in processing_results[process_id]:
        processing_results[process_id]["pdf_files"] = uploaded_files[process_id]

    return processing_results[process_id]

async def process_files_from_disk_async(process_id: str, brd_path: str, pdf_paths: List[str]):
    """
    Async version: Process the uploaded files from disk in the background.

    Args:
        process_id: The process ID
        brd_path: Path to the BRD file on disk
        pdf_paths: List of paths to PDF files on disk
    """
    import time
    from app.logger import log_execution

    def update_progress(step, total_steps, message, progress, operation="", **kwargs):
        """Helper function to update progress with detailed information"""
        current_time = time.time()
        processing_results[process_id].update({
            "status": "processing",
            "message": message,
            "progress": progress,
            "current_step": step,
            "total_steps": total_steps,
            "current_operation": operation,
            "last_updated": current_time,
            **kwargs
        })
        # Add to detailed steps log
        if "detailed_steps" not in processing_results[process_id]:
            processing_results[process_id]["detailed_steps"] = []

        processing_results[process_id]["detailed_steps"].append({
            "step": step,
            "message": message,
            "operation": operation,
            "timestamp": current_time,
            "progress": progress
        })

    try:
        # Initialize processing status with detailed tracking
        start_time = time.time()
        processing_results[process_id] = {
            "status": "processing",
            "message": "Initializing extraction process...",
            "progress": 0,
            "current_step": 0,
            "total_steps": 6,
            "detailed_steps": [],
            "current_operation": "Initializing",
            "files_processed": 0,
            "total_files": len(pdf_paths),
            "chunks_processed": 0,
            "total_chunks": 0,
            "start_time": start_time,
            "last_updated": start_time
        }

        # Initialize uploaded files tracking for this process
        uploaded_files[process_id] = []

        # Copy PDF files to the uploads directory for viewing
        update_progress(0, 6, "Preparing uploaded files for processing...", 5, "Copying files")

        for i, pdf_path in enumerate(pdf_paths):
            filename = os.path.basename(pdf_path)
            # Create a unique filename to avoid collisions
            unique_filename = f"{process_id}_{filename}"
            dest_path = os.path.join(PDF_UPLOADS_DIR, unique_filename)

            # Update progress for each file
            update_progress(0, 6, f"Copying file {i+1}/{len(pdf_paths)}: {filename}", 5 + (i * 3 // len(pdf_paths)), f"Copying {filename}")

            # Copy the file
            shutil.copy2(pdf_path, dest_path)

            # Store information about the uploaded file
            uploaded_files[process_id].append({
                "original_filename": filename,
                "stored_filename": unique_filename,
                "original_path": pdf_path,
                "url_path": f"/pdfs/{unique_filename}"
            })

        # Update status - Step 1: Reading BRD
        status_message = "Step 1/6: Reading BRD document"
        print(f"\n[INFO] {status_message}")
        update_progress(1, 6, status_message, 10, "Reading BRD file")
        processing_results[process_id]["pdf_files"] = uploaded_files[process_id]

        # Extract text from BRD asynchronously
        brd_text = await extract_text_from_brd_async(brd_path)
        update_progress(1, 6, f"Step 1/6: BRD document read successfully ({len(brd_text)} characters)", 15, "BRD text extracted")

        # Update status - Step 2: Identifying requirements
        status_message = "Step 2/6: Analyzing BRD and extracting structured requirements using GPT-4"
        print(f"[INFO] {status_message}")
        update_progress(2, 6, status_message, 20, "Analyzing BRD with GPT-4")

        # Identify structured requirements from BRD asynchronously
        requirements = await identify_requirements_async(brd_text)

        # Update progress after requirements identification
        req_count = len(requirements) if requirements else 0
        update_progress(2, 6, f"Step 2/6: Identified {req_count} requirements from BRD", 25, "Requirements extracted")

        # Update status - Step 3: Reading PDFs/PKLs
        status_message = f"Step 3/6: Reading {len(pdf_paths)} document(s)"
        print(f"[INFO] {status_message}")

        # Count data requirements
        data_requirements_count = 0
        if requirements and len(requirements) > 0:
            data_requirements = requirements[0].get("data_requirements", [])
            data_requirements_count = len(data_requirements)
            print(f"[DETAILS] Found {data_requirements_count} data requirements in BRD")

            # Log project info if available
            project_info = requirements[0].get("project_info", {})
            if project_info:
                print(f"[DETAILS] Project: {project_info.get('title', 'Unknown')}")
                print(f"[DETAILS] Objective: {project_info.get('objective', '')[:100]}...")

        update_progress(3, 6, status_message, 30, "Preparing to read documents",
                       details={
                           "requirements_count": data_requirements_count,
                           "pdf_count": len(pdf_paths)
                       })

        # Process each document (PDF or PKL) asynchronously
        all_chunks = []
        for i, pdf_path in enumerate(pdf_paths):
            # Update status for each document (PDF or PKL)
            doc_name = os.path.basename(pdf_path)
            file_ext = os.path.splitext(doc_name)[1].lower()
            file_type = "PKL" if file_ext == ".pkl" else "PDF"
            status_message = f"Step 3/6: Reading {file_type} {i+1}/{len(pdf_paths)} - {doc_name}"
            print(f"[INFO] {status_message}")

            # Calculate progress within step 3 (30-40%)
            step_progress = 30 + (i * 10 // len(pdf_paths))
            update_progress(3, 6, status_message, step_progress, f"Reading {file_type} file",
                           files_processed=i,
                           current_file=doc_name)

            # Extract text from PDF asynchronously
            pdf_pages = await extract_text_from_pdf_async(pdf_path)

            # Update progress after text extraction
            update_progress(3, 6, f"Step 3/6: Extracted text from {doc_name} ({len(pdf_pages)} pages)",
                           step_progress + 2, f"Text extracted from {file_type}",
                           files_processed=i,
                           current_file=doc_name,
                           pages_extracted=len(pdf_pages))

            # Chunk the PDF text
            chunks = chunk_pdf_text(pdf_pages)
            all_chunks.extend(chunks)

            # Update progress after chunking
            update_progress(3, 6, f"Step 3/6: Generated {len(chunks)} chunks from {doc_name}",
                           step_progress + 4, f"Text chunked from {file_type}",
                           files_processed=i+1,
                           current_file=doc_name,
                           chunks_generated=len(chunks),
                           total_chunks_so_far=len(all_chunks))

        # Update status - Step 4: Processing chunks
        status_message = f"Step 4/6: Preparing to process {len(all_chunks)} text chunks"
        print(f"[INFO] {status_message}")
        print(f"[DETAILS] Generated {len(all_chunks)} chunks from {len(pdf_paths)} documents")

        # Update total chunks count for progress tracking
        processing_results[process_id]["total_chunks"] = len(all_chunks)

        update_progress(4, 6, status_message, 50, "Preparing chunk processing",
                       details={
                           "requirements_count": len(requirements),
                           "chunks_count": len(all_chunks)
                       },
                       total_chunks=len(all_chunks))

        # Process each requirement
        extraction_results = []
        operation_count = 0

        for req_idx, requirement in enumerate(requirements):
            # Update status - Step 5: Processing chunks for this requirement
            status_message = f"Step 5/6: Processing requirement {req_idx+1}/{len(requirements)}"
            print(f"\n[INFO] {status_message}")

            # Get max workers from settings
            max_workers = settings.max_parallel_workers

            # Define async progress callback for real-time chunk processing updates
            async def chunk_progress_callback(completed_chunks, total_chunks, result):
                """Async callback to update progress during chunk processing"""
                # Calculate progress within step 5 (50-80%)
                chunk_progress = 50 + (completed_chunks / total_chunks * 30)

                # Update progress with detailed chunk information
                update_progress(5, 6,
                               f"Step 5/6: Processing chunks {completed_chunks}/{total_chunks} for requirement {req_idx+1}/{len(requirements)}",
                               chunk_progress,
                               f"Processing chunk {completed_chunks}/{total_chunks}",
                               chunks_processed=completed_chunks,
                               total_chunks=total_chunks,
                               current_requirement=req_idx + 1,
                               total_requirements=len(requirements))

            # Process chunks in parallel asynchronously with progress callback
            chunk_results = await process_chunks_in_parallel_async(requirement, all_chunks, max_workers=max_workers, progress_callback=chunk_progress_callback)

            # Update status after parallel processing
            status_message = f"Step 5/6: Processing requirement {req_idx+1}/{len(requirements)} - Completed parallel processing"
            print(f"[INFO] {status_message}")

            # Update progress
            update_progress(5, 6, status_message, 80, "Parallel processing completed",
                           details={
                               "current_requirement": req_idx + 1,
                               "total_requirements": len(requirements),
                               "completed_chunks": len(all_chunks),
                               "total_chunks": len(all_chunks)
                           })

            # Aggregate results for this requirement
            update_progress(5, 6, f"Step 5/6: Aggregating results for requirement {req_idx+1}/{len(requirements)}",
                           82, "Aggregating results")
            aggregated = aggregate_results(requirement, chunk_results)
            extraction_results.append(aggregated)

            # Update operation count for overall progress tracking
            operation_count += len(all_chunks)

        # Update status - Step 6: Finalizing results
        status_message = "Step 6/6: Finalizing extraction results"
        print(f"\n[INFO] {status_message}")
        print(f"[DETAILS] Aggregating results for {len(extraction_results)} requirements")
        update_progress(6, 6, status_message, 90, "Finalizing results")

        # Format the final output
        update_progress(6, 6, "Step 6/6: Formatting final output", 95, "Formatting output")
        final_output = format_json_output(extraction_results)

        # Update status with results
        status_message = "Extraction process completed successfully"
        print(f"\n[SUCCESS] {status_message}")
        print(f"[DETAILS] Extracted information for {len(extraction_results)} requirements")

        # Check if we're using the new structured format
        if len(extraction_results) == 1 and "key_points" in extraction_results[0]:
            # For structured BRD
            item = extraction_results[0]
            key_points = item.get("key_points", [])
            addressed_points = item.get("addressed_points", [])

            # Count how many key points were addressed
            addressed_count = len(addressed_points)
            total_points = len(key_points)

            if total_points > 0:
                percentage = addressed_count / total_points * 100
                print(f"[SUMMARY] Addressed {addressed_count}/{total_points} key points ({percentage:.1f}%)")
                status_message = f"Extraction completed: {addressed_count}/{total_points} key points addressed ({percentage:.1f}%)"
            else:
                print("[SUMMARY] No key points were identified in the BRD")
                status_message = "Extraction completed: No key points identified in BRD"
        else:
            # For legacy format - count found requirements
            found_count = sum(1 for item in extraction_results if item.get("found", False))
            if extraction_results:
                percentage = found_count/len(extraction_results)*100
                print(f"[SUMMARY] Found relevant information for {found_count}/{len(extraction_results)} requirements ({percentage:.1f}%)")
                status_message = f"Extraction completed: {found_count}/{len(extraction_results)} requirements found ({percentage:.1f}%)"
            else:
                print("[SUMMARY] No requirements were processed")
                status_message = "Extraction completed: No requirements processed"

        # Calculate total processing time
        total_time = time.time() - start_time

        processing_results[process_id].update({
            "status": "completed",
            "message": status_message,
            "progress": 100,
            "current_operation": "Completed",
            "processing_time": total_time,
            "result": final_output
        })

    except Exception as e:
        error_message = f"Error during extraction process: {str(e)}"
        print(f"\n[ERROR] {error_message}")
        log_execution(f"Error in async background processing: {str(e)}", "ERROR")

        processing_results[process_id].update({
            "status": "failed",
            "message": error_message,
            "progress": 0,
            "error": str(e)
        })

    finally:
        # Clean up temporary directory
        if os.path.exists(brd_path):
            temp_dir = os.path.dirname(brd_path)
            try:
                shutil.rmtree(temp_dir)
                print(f"[INFO] Cleaned up temporary directory: {temp_dir}")
            except Exception as cleanup_error:
                print(f"[WARNING] Could not clean up temporary directory {temp_dir}: {str(cleanup_error)}")



async def process_files_from_disk(process_id: str, brd_path: str, pdf_paths: List[str]):
    """
    Process the uploaded files from disk in the background.

    Args:
        process_id: The process ID
        brd_path: Path to the BRD file on disk
        pdf_paths: List of paths to PDF files on disk
    """
    import time
    from app.logger import log_execution

    def update_progress(step, total_steps, message, progress, operation="", **kwargs):
        """Helper function to update progress with detailed information"""
        current_time = time.time()
        processing_results[process_id].update({
            "status": "processing",
            "message": message,
            "progress": progress,
            "current_step": step,
            "total_steps": total_steps,
            "current_operation": operation,
            "last_updated": current_time,
            **kwargs
        })
        # Add to detailed steps log
        if "detailed_steps" not in processing_results[process_id]:
            processing_results[process_id]["detailed_steps"] = []

        processing_results[process_id]["detailed_steps"].append({
            "step": step,
            "message": message,
            "operation": operation,
            "timestamp": current_time,
            "progress": progress
        })

    try:
        # Initialize processing status with detailed tracking
        start_time = time.time()
        processing_results[process_id] = {
            "status": "processing",
            "message": "Initializing extraction process...",
            "progress": 0,
            "current_step": 0,
            "total_steps": 6,
            "detailed_steps": [],
            "current_operation": "Initializing",
            "files_processed": 0,
            "total_files": len(pdf_paths),
            "chunks_processed": 0,
            "total_chunks": 0,
            "start_time": start_time,
            "last_updated": start_time
        }

        # Initialize uploaded files tracking for this process
        uploaded_files[process_id] = []

        # Copy PDF files to the uploads directory for viewing
        update_progress(0, 6, "Preparing uploaded files for processing...", 5, "Copying files")

        for i, pdf_path in enumerate(pdf_paths):
            filename = os.path.basename(pdf_path)
            # Create a unique filename to avoid collisions
            unique_filename = f"{process_id}_{filename}"
            dest_path = os.path.join(PDF_UPLOADS_DIR, unique_filename)

            # Update progress for each file
            update_progress(0, 6, f"Copying file {i+1}/{len(pdf_paths)}: {filename}", 5 + (i * 3 // len(pdf_paths)), f"Copying {filename}")

            # Copy the file
            shutil.copy2(pdf_path, dest_path)

            # Store information about the uploaded file
            uploaded_files[process_id].append({
                "original_filename": filename,
                "stored_filename": unique_filename,
                "original_path": pdf_path,
                "url_path": f"/pdfs/{unique_filename}"
            })

        # Update status - Step 1: Reading BRD
        status_message = "Step 1/6: Reading BRD document"
        print(f"\n[INFO] {status_message}")
        update_progress(1, 6, status_message, 10, "Reading BRD file")
        processing_results[process_id]["pdf_files"] = uploaded_files[process_id]

        # Extract text from BRD
        brd_text = extract_text_from_brd(brd_path)
        update_progress(1, 6, f"Step 1/6: BRD document read successfully ({len(brd_text)} characters)", 15, "BRD text extracted")

        # Update status - Step 2: Identifying requirements
        status_message = "Step 2/6: Analyzing BRD and extracting structured requirements using GPT-4"
        print(f"[INFO] {status_message}")
        update_progress(2, 6, status_message, 20, "Analyzing BRD with GPT-4")

        # Identify structured requirements from BRD
        requirements = identify_requirements(brd_text)

        # Update progress after requirements identification
        req_count = len(requirements) if requirements else 0
        update_progress(2, 6, f"Step 2/6: Identified {req_count} requirements from BRD", 25, "Requirements extracted")

        # Update status - Step 3: Reading PDFs/PKLs
        status_message = f"Step 3/6: Reading {len(pdf_paths)} document(s)"
        print(f"[INFO] {status_message}")

        # Count data requirements
        data_requirements_count = 0
        if requirements and len(requirements) > 0:
            data_requirements = requirements[0].get("data_requirements", [])
            data_requirements_count = len(data_requirements)
            print(f"[DETAILS] Found {data_requirements_count} data requirements in BRD")

            # Log project info if available
            project_info = requirements[0].get("project_info", {})
            if project_info:
                print(f"[DETAILS] Project: {project_info.get('title', 'Unknown')}")
                print(f"[DETAILS] Objective: {project_info.get('objective', '')[:100]}...")

        update_progress(3, 6, status_message, 30, "Preparing to read documents",
                       details={
                           "requirements_count": data_requirements_count,
                           "pdf_count": len(pdf_paths)
                       })

        # Process each document (PDF or PKL)
        all_chunks = []
        for i, pdf_path in enumerate(pdf_paths):
            # Update status for each document (PDF or PKL)
            doc_name = os.path.basename(pdf_path)
            file_ext = os.path.splitext(doc_name)[1].lower()
            file_type = "PKL" if file_ext == ".pkl" else "PDF"
            status_message = f"Step 3/6: Reading {file_type} {i+1}/{len(pdf_paths)} - {doc_name}"
            print(f"[INFO] {status_message}")

            # Calculate progress within step 3 (30-40%)
            step_progress = 30 + (i * 10 // len(pdf_paths))
            update_progress(3, 6, status_message, step_progress, f"Reading {file_type} file",
                           files_processed=i,
                           current_file=doc_name)

            # Extract text from PDF
            pdf_pages = extract_text_from_pdf(pdf_path)

            # Update progress after text extraction
            update_progress(3, 6, f"Step 3/6: Extracted text from {doc_name} ({len(pdf_pages)} pages)",
                           step_progress + 2, f"Text extracted from {file_type}",
                           files_processed=i,
                           current_file=doc_name,
                           pages_extracted=len(pdf_pages))

            # Chunk the PDF text
            chunks = chunk_pdf_text(pdf_pages)
            all_chunks.extend(chunks)

            # Update progress after chunking
            update_progress(3, 6, f"Step 3/6: Generated {len(chunks)} chunks from {doc_name}",
                           step_progress + 4, f"Text chunked from {file_type}",
                           files_processed=i+1,
                           current_file=doc_name,
                           chunks_generated=len(chunks),
                           total_chunks_so_far=len(all_chunks))

        # Update status - Step 4: Processing chunks
        status_message = f"Step 4/6: Preparing to process {len(all_chunks)} text chunks"
        print(f"[INFO] {status_message}")
        print(f"[DETAILS] Generated {len(all_chunks)} chunks from {len(pdf_paths)} documents")

        # Update total chunks count for progress tracking
        processing_results[process_id]["total_chunks"] = len(all_chunks)

        update_progress(4, 6, status_message, 50, "Preparing chunk processing",
                       details={
                           "requirements_count": len(requirements),
                           "chunks_count": len(all_chunks)
                       },
                       total_chunks=len(all_chunks))

        # Process each requirement against all chunks
        extraction_results = []
        operation_count = 0

        for req_idx, requirement in enumerate(requirements):
            # For structured requirements, we need to handle them differently
            if req_idx == 0 and "project_info" in requirement:
                # This is a structured BRD analysis
                project_info = requirement.get("project_info", {})
                project_title = project_info.get("title", "Unknown Project")
                data_requirements = requirement.get("data_requirements", [])

                status_message = f"Step 5/6: Processing structured BRD with {len(data_requirements)} data requirements"
                print(f"\n[INFO] {status_message}")
                print(f"[DETAILS] Project: {project_title}")
                print(f"[DETAILS] Data Requirements: {len(data_requirements)}")
            else:
                # Legacy format
                req_id = requirement.get("requirement_id", f"REQ-{req_idx+1}")
                status_message = f"Step 5/6: Processing requirement {req_idx+1}/{len(requirements)} - {req_id}"
                print(f"\n[INFO] {status_message}")
                print(f"[DETAILS] Requirement text: {requirement.get('requirement_text', '')[:100]}...")
            # Update processing results with appropriate details
            if req_idx == 0 and "project_info" in requirement:
                # For structured BRD
                project_info = requirement.get("project_info", {})
                data_requirements = requirement.get("data_requirements", [])

                processing_results[process_id].update({
                    "status": "processing",
                    "message": status_message,
                    "progress": 50 + (req_idx * 30 // len(requirements)),
                    "current_step": 5,
                    "total_steps": 6,
                    "details": {
                        "project_title": project_info.get("title", "Unknown Project"),
                        "data_requirements_count": len(data_requirements),
                        "business_rules_count": len(requirement.get("business_rules", [])),
                        "output_formats_count": len(requirement.get("output_formats", []))
                    }
                })
            else:
                # For legacy format
                req_id = requirement.get("requirement_id", f"REQ-{req_idx+1}")

                processing_results[process_id].update({
                    "status": "processing",
                    "message": status_message,
                    "progress": 50 + (req_idx * 30 // len(requirements)),
                    "current_step": 5,
                    "total_steps": 6,
                    "details": {
                        "current_requirement": req_idx + 1,
                        "total_requirements": len(requirements),
                        "requirement_id": req_id
                    }
                })

            # Process chunks in parallel using concurrent.futures
            # Use the max_parallel_workers from config, but limit by the number of chunks
            max_workers = min(settings.max_parallel_workers, len(all_chunks))

            # Update status for processing
            status_message = f"Step 5/6: Processing requirement {req_idx+1}/{len(requirements)} - Starting parallel processing of {len(all_chunks)} chunks"
            print(f"[INFO] {status_message}")

            # Update processing details based on requirement type
            if req_idx == 0 and "project_info" in requirement:
                # For structured BRD
                processing_results[process_id].update({
                    "message": status_message,
                    "progress": 50,
                    "details": {
                        "project_title": requirement.get("project_info", {}).get("title", "Unknown Project"),
                        "data_requirements_count": len(requirement.get("data_requirements", [])),
                        "total_chunks": len(all_chunks)
                    }
                })
            else:
                # For legacy format
                processing_results[process_id].update({
                    "message": status_message,
                    "progress": 50,
                    "details": {
                        "current_requirement": req_idx + 1,
                        "total_requirements": len(requirements),
                        "total_chunks": len(all_chunks)
                    }
                })

            # Define progress callback for real-time chunk processing updates
            def chunk_progress_callback(completed_chunks, total_chunks, result):
                """Callback to update progress during chunk processing"""
                # Calculate progress within step 5 (50-80%)
                chunk_progress = 50 + (completed_chunks / total_chunks * 30)

                # Update progress with detailed chunk information
                update_progress(5, 6,
                               f"Step 5/6: Processing chunks {completed_chunks}/{total_chunks} for requirement {req_idx+1}/{len(requirements)}",
                               chunk_progress,
                               f"Processing chunk {completed_chunks}/{total_chunks}",
                               chunks_processed=completed_chunks,
                               total_chunks=total_chunks,
                               current_requirement=req_idx + 1,
                               total_requirements=len(requirements))

            # Process chunks in parallel with progress callback
            chunk_results = process_chunks_in_parallel(requirement, all_chunks, max_workers=max_workers, progress_callback=chunk_progress_callback)

            # Update status after parallel processing
            status_message = f"Step 5/6: Processing requirement {req_idx+1}/{len(requirements)} - Completed parallel processing"
            print(f"[INFO] {status_message}")

            # Update progress
            update_progress(5, 6, status_message, 80, "Parallel processing completed",
                           details={
                               "current_requirement": req_idx + 1,
                               "total_requirements": len(requirements),
                               "completed_chunks": len(all_chunks),
                               "total_chunks": len(all_chunks)
                           })

            # Aggregate results for this requirement
            update_progress(5, 6, f"Step 5/6: Aggregating results for requirement {req_idx+1}/{len(requirements)}",
                           82, "Aggregating results")
            aggregated = aggregate_results(requirement, chunk_results)
            extraction_results.append(aggregated)

            # Update operation count for overall progress tracking
            operation_count += len(all_chunks)

        # Update status - Step 6: Finalizing results
        status_message = "Step 6/6: Finalizing extraction results"
        print(f"\n[INFO] {status_message}")
        print(f"[DETAILS] Aggregating results for {len(extraction_results)} requirements")
        update_progress(6, 6, status_message, 90, "Finalizing results")

        # Format the final output
        update_progress(6, 6, "Step 6/6: Formatting final output", 95, "Formatting output")
        final_output = format_json_output(extraction_results)

        # Update status with results
        status_message = "Extraction process completed successfully"
        print(f"\n[SUCCESS] {status_message}")
        print(f"[DETAILS] Extracted information for {len(extraction_results)} requirements")

        # Check if we're using the new structured format
        if len(extraction_results) == 1 and "key_points" in extraction_results[0]:
            # For structured BRD
            item = extraction_results[0]
            key_points = item.get("key_points", [])
            addressed_points = item.get("addressed_points", [])

            # Count how many key points were addressed
            addressed_count = len(addressed_points)
            total_points = len(key_points)

            if total_points > 0:
                percentage = addressed_count / total_points * 100
                print(f"[SUMMARY] Addressed {addressed_count}/{total_points} key points ({percentage:.1f}%)")
                status_message = f"Extraction completed: {addressed_count}/{total_points} key points addressed ({percentage:.1f}%)"
            else:
                print("[SUMMARY] No key points were identified in the BRD")
                status_message = "Extraction completed: No key points identified in BRD"
        else:
            # For legacy format - count found requirements
            found_count = sum(1 for item in extraction_results if item.get("found", False))
            if extraction_results:
                percentage = found_count/len(extraction_results)*100
                print(f"[SUMMARY] Found relevant information for {found_count}/{len(extraction_results)} requirements ({percentage:.1f}%)")
                status_message = f"Extraction completed: {found_count}/{len(extraction_results)} requirements found ({percentage:.1f}%)"
            else:
                print("[SUMMARY] No requirements were processed")
                status_message = "Extraction completed: No requirements processed"

        # Calculate total processing time
        total_time = time.time() - start_time

        processing_results[process_id].update({
            "status": "completed",
            "message": status_message,
            "progress": 100,
            "current_operation": "Completed",
            "processing_time": total_time,
            "result": final_output
        })

    except Exception as e:
        # Update status with error
        error_message = f"Extraction process failed: {str(e)}"
        print(f"\n[ERROR] {error_message}")
        print(f"[DETAILS] Exception: {type(e).__name__}")
        import traceback
        print(f"[TRACEBACK] {traceback.format_exc()}")

        processing_results[process_id].update({
            "status": "failed",
            "message": error_message,
            "progress": 0,
            "error": str(e)
        })


@app.get("/download/{filename}")
async def download_file(filename: str):
    """
    Download a file (PDF or PKL).

    Args:
        filename: The filename to download

    Returns:
        FileResponse: The requested file
    """
    # Check if the file exists in the uploads directory
    file_path = os.path.join(PDF_UPLOADS_DIR, filename)

    # If the file doesn't exist, check if it's a process-specific file
    if not os.path.exists(file_path):
        # Try to find the file with a process prefix
        for process_id in uploaded_files:
            for file_info in uploaded_files[process_id]:
                if file_info["original_filename"] == filename:
                    file_path = os.path.join(PDF_UPLOADS_DIR, file_info["stored_filename"])
                    if os.path.exists(file_path):
                        break

    # If still not found, check if it's in the pickles directory
    if not os.path.exists(file_path):
        filename_without_ext = os.path.splitext(filename)[0]
        pickle_path = os.path.join('pickles', f"{filename_without_ext}.pkl")
        if os.path.exists(pickle_path):
            file_path = pickle_path

    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=404,
            detail=f"File {filename} not found"
        )

    # Determine the media type based on file extension
    file_ext = os.path.splitext(filename)[1].lower()
    if file_ext == ".pdf":
        media_type = "application/pdf"
    elif file_ext == ".pkl":
        media_type = "application/octet-stream"
    else:
        media_type = "application/octet-stream"

    return FileResponse(
        file_path,
        media_type=media_type,
        filename=filename
    )


@app.get("/pickle/{pdf_filename}")
async def get_pickle_for_pdf(pdf_filename: str):
    """
    Get the pickle file for a PDF if it exists.

    Args:
        pdf_filename: The PDF filename to get the pickle for

    Returns:
        FileResponse: The pickle file
    """
    from app.logger import log_execution

    # Get the filename without extension
    filename_without_ext = os.path.splitext(pdf_filename)[0]

    # Check various locations for the pickle file
    pickle_locations = [
        os.path.join('pickles', f"{filename_without_ext}.pkl"),  # Relative to current directory
        os.path.join(os.path.dirname(PDF_UPLOADS_DIR), 'pickles', f"{filename_without_ext}.pkl"),  # Next to uploads
        os.path.abspath(f'/app/pickles/{filename_without_ext}.pkl'),  # Docker container path
    ]

    # Try each location
    for pickle_path in pickle_locations:
        if os.path.exists(pickle_path):
            log_execution(f"Found pickle file for {pdf_filename} at {pickle_path}", "INFO")
            return FileResponse(
                pickle_path,
                media_type="application/octet-stream",
                filename=f"{filename_without_ext}.pkl"
            )

    # If no pickle file found, return 404
    raise HTTPException(
        status_code=404,
        detail=f"No pickle file found for {pdf_filename}"
    )


if __name__ == "__main__":
    # For development, use reload=True with single worker
    # For production, use multiple workers without reload
    import os

    # Check if we're in development mode
    is_development = os.getenv("ENVIRONMENT", "development") == "development"

    if is_development:
        # Development mode: single worker with reload
        uvicorn.run(
            "app.main:app",
            host=settings.api_host,
            port=settings.api_port,
            reload=True,
            log_level="info"
        )
    else:
        # Production mode: multiple workers without reload
        uvicorn.run(
            "app.main:app",
            host=settings.api_host,
            port=settings.api_port,
            workers=4,  # 4 workers for better concurrency
            log_level="info"
        )