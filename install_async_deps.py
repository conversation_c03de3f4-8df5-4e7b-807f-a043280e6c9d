#!/usr/bin/env python3
"""
<PERSON>ript to install the new async dependencies for the enhanced API.
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Main function to install dependencies."""
    print("🔧 Installing Async Dependencies for Enhanced API")
    print("=" * 50)
    
    # List of new dependencies
    dependencies = [
        "aiofiles==23.2.1",
        "httpx==0.25.2",
        "pyyaml==6.0.1"  # In case it's missing
    ]
    
    success_count = 0
    total_count = len(dependencies)
    
    for dep in dependencies:
        print(f"\n📦 Installing {dep}...")
        if install_package(dep):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"Installation Summary: {success_count}/{total_count} packages installed successfully")
    
    if success_count == total_count:
        print("🎉 All dependencies installed successfully!")
        print("\nYou can now run the async API server with:")
        print("  python start_server.py --workers 4")
        print("  or")
        print("  python start_server.py --reload  # for development")
    else:
        print("⚠️  Some dependencies failed to install. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
