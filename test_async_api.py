#!/usr/bin/env python3
"""
Test script for the async API implementation.
Tests concurrent requests to verify async functionality.
"""

import asyncio
import aiohttp
import time
import json
from pathlib import Path

# Configuration
API_URL = "http://localhost:8001"
TEST_CONCURRENT_REQUESTS = 3

async def test_api_health():
    """Test if the API is running."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{API_URL}/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ API is running: {data.get('message', 'Unknown')}")
                    return True
                else:
                    print(f"❌ API returned status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Failed to connect to API: {str(e)}")
        return False

async def upload_and_process_files(session, request_id, brd_file, pdf_file):
    """Upload files and start processing."""
    try:
        # Prepare files for upload
        data = aiohttp.FormData()
        
        # Add BRD file
        data.add_field('brd_file', 
                      open(brd_file, 'rb'), 
                      filename=brd_file.name,
                      content_type='application/octet-stream')
        
        # Add PDF file
        data.add_field('pdf_files', 
                      open(pdf_file, 'rb'), 
                      filename=pdf_file.name,
                      content_type='application/pdf')
        
        print(f"🚀 Request {request_id}: Starting upload...")
        start_time = time.time()
        
        async with session.post(f"{API_URL}/upload-and-process/", data=data) as response:
            if response.status == 200:
                result = await response.json()
                process_id = result.get("process_id")
                upload_time = time.time() - start_time
                print(f"✅ Request {request_id}: Upload completed in {upload_time:.2f}s, Process ID: {process_id}")
                return process_id
            else:
                error_text = await response.text()
                print(f"❌ Request {request_id}: Upload failed with status {response.status}: {error_text}")
                return None
                
    except Exception as e:
        print(f"❌ Request {request_id}: Upload error: {str(e)}")
        return None

async def monitor_process(session, request_id, process_id):
    """Monitor the processing status."""
    try:
        start_time = time.time()
        last_progress = 0
        
        while True:
            async with session.get(f"{API_URL}/status/{process_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    status = data.get("status", "unknown")
                    progress = data.get("progress", 0)
                    message = data.get("message", "No message")
                    
                    # Only log progress updates
                    if progress != last_progress:
                        elapsed = time.time() - start_time
                        print(f"📊 Request {request_id}: {progress}% - {message[:50]}... ({elapsed:.1f}s)")
                        last_progress = progress
                    
                    if status == "completed":
                        total_time = time.time() - start_time
                        print(f"🎉 Request {request_id}: Completed in {total_time:.2f}s")
                        return True
                    elif status == "failed":
                        error = data.get("error", "Unknown error")
                        print(f"❌ Request {request_id}: Failed - {error}")
                        return False
                        
                else:
                    print(f"❌ Request {request_id}: Status check failed with {response.status}")
                    return False
            
            # Wait before next check
            await asyncio.sleep(2)
            
    except Exception as e:
        print(f"❌ Request {request_id}: Monitoring error: {str(e)}")
        return False

async def test_single_request():
    """Test a single request to verify basic functionality."""
    print("\n🧪 Testing Single Request")
    print("-" * 40)
    
    # Check for sample files
    brd_file = Path("sample_brd.docx")
    pdf_file = Path("sample_document.pdf")
    
    if not brd_file.exists():
        print(f"❌ Sample BRD file not found: {brd_file}")
        print("Please place a sample BRD file in the current directory")
        return False
    
    if not pdf_file.exists():
        print(f"❌ Sample PDF file not found: {pdf_file}")
        print("Please place a sample PDF file in the current directory")
        return False
    
    async with aiohttp.ClientSession() as session:
        # Upload and start processing
        process_id = await upload_and_process_files(session, 1, brd_file, pdf_file)
        
        if process_id:
            # Monitor processing
            success = await monitor_process(session, 1, process_id)
            return success
        else:
            return False

async def test_concurrent_requests():
    """Test multiple concurrent requests to verify async functionality."""
    print(f"\n🧪 Testing {TEST_CONCURRENT_REQUESTS} Concurrent Requests")
    print("-" * 40)
    
    # Check for sample files
    brd_file = Path("sample_brd.docx")
    pdf_file = Path("sample_document.pdf")
    
    if not brd_file.exists() or not pdf_file.exists():
        print("❌ Sample files not found. Skipping concurrent test.")
        return False
    
    async with aiohttp.ClientSession() as session:
        # Start multiple requests concurrently
        start_time = time.time()
        
        # Upload all requests concurrently
        upload_tasks = [
            upload_and_process_files(session, i+1, brd_file, pdf_file)
            for i in range(TEST_CONCURRENT_REQUESTS)
        ]
        
        process_ids = await asyncio.gather(*upload_tasks)
        
        # Filter out failed uploads
        valid_processes = [(i+1, pid) for i, pid in enumerate(process_ids) if pid is not None]
        
        if not valid_processes:
            print("❌ All uploads failed")
            return False
        
        print(f"✅ {len(valid_processes)} uploads successful, monitoring processing...")
        
        # Monitor all processes concurrently
        monitor_tasks = [
            monitor_process(session, req_id, process_id)
            for req_id, process_id in valid_processes
        ]
        
        results = await asyncio.gather(*monitor_tasks)
        
        total_time = time.time() - start_time
        successful = sum(results)
        
        print(f"\n📊 Concurrent Test Results:")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Successful: {successful}/{len(valid_processes)}")
        print(f"   Average time per request: {total_time/len(valid_processes):.2f}s")
        
        return successful == len(valid_processes)

async def main():
    """Main test function."""
    print("🧪 Async API Test Suite")
    print("=" * 50)
    
    # Test API health
    if not await test_api_health():
        print("\n❌ API is not running. Please start the server first:")
        print("   python start_server.py --workers 4")
        return 1
    
    # Test single request
    single_success = await test_single_request()
    
    # Test concurrent requests
    concurrent_success = await test_concurrent_requests()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"   Single Request: {'✅ PASS' if single_success else '❌ FAIL'}")
    print(f"   Concurrent Requests: {'✅ PASS' if concurrent_success else '❌ FAIL'}")
    
    if single_success and concurrent_success:
        print("\n🎉 All tests passed! Async API is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Check the logs above for details.")
        return 1

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        exit(result)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        exit(1)
